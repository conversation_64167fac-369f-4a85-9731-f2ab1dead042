extends Node2D

# Scéna 1.1 - Základná scéna pre Prekliateho Dedičstvo
# Autor: [Va<PERSON>e meno]
# Dátum: [Aktu<PERSON>lny dátum]

# Referencie na uzly
@onready var zvuk_ambient: AudioStreamPlayer = $Zvuk_Ambient
@onready var zvuk_efekty: AudioStreamPlayer = $Zvuk_Efekty
@onready var ui_hlavolam: CanvasLayer = $UI_Hlavolam
@onready var vstupne_pole: LineEdit = $UI_Hlavolam/VstupnePole
@onready var tlacitko_overit: Button = $UI_Hlavolam/TlacitkoOverit
@onready var blesk: ColorRect = $Blesk
@onready var start_button: Button = $StartButton

# Správne riešenie hlavolamu
const SPRAVNE_RIESENIE = "GROFKAJEVKRYPTE"

# Called when the node enters the scene tree for the first time.
func _ready():
	print("Scéna 1.1 je načítaná")

	# Pripo<PERSON>nie sign<PERSON>lov
	tlacitko_overit.pressed.connect(_on_tlacitko_overit_pressed)
	zvuk_efekty.finished.connect(_on_zvuk_efekty_finished)
	start_button.pressed.connect(_on_start_button_pressed)

	# UI je na začiatku skryté
	ui_hlavolam.visible = false
	print("UI je skryté, čaká sa na spustenie")

	# Spustenie úvodnej sekvencie
	_start_intro_sequence()

func _start_intro_sequence():
	print("Spúšťa sa úvodná sekvencia...")

	# Vytvorenie jednoduchého ambientného zvuku pomocou AudioStreamGenerator
	_create_ambient_sound()

	# Simulácia úvodného monológu pomocou časovača
	print("Rozprávač: Vitajte v tajomnom kočiari...")
	await get_tree().create_timer(3.0).timeout

	# Po 'skončení monológu' zobrazí UI
	_on_zvuk_efekty_finished()

func _create_ambient_sound():
	# Vytvorenie jednoduchého ambientného zvuku
	var generator = AudioStreamGenerator.new()
	generator.mix_rate = 22050
	generator.buffer_length = 0.1
	zvuk_ambient.stream = generator
	zvuk_ambient.play()

func _on_zvuk_efekty_finished():
	# Po skončení monológu zviditeľní UI hlavolamu
	ui_hlavolam.visible = true

func _on_start_button_pressed():
	print("Spúšťa sa hádanka...")
	start_button.visible = false
	ui_hlavolam.visible = true

func _on_tlacitko_overit_pressed():
	var odpoved = vstupne_pole.text
	var normalizovana_odpoved = _normalizuj_text(odpoved)

	if normalizovana_odpoved == SPRAVNE_RIESENIE:
		print("Správna odpoveď!")
		_play_success_sound()
		ui_hlavolam.visible = false
		_start_end_sequence()
	else:
		print("Nesprávna odpoveď!")
		_play_fail_sound()
		vstupne_pole.text = ""

func _play_success_sound():
	# Vytvorenie úspešného zvuku pomocou tween
	var tween = create_tween()
	tween.tween_method(_generate_success_tone, 440.0, 880.0, 0.5)

func _play_fail_sound():
	# Vytvorenie neúspešného zvuku
	var tween = create_tween()
	tween.tween_method(_generate_fail_tone, 220.0, 110.0, 0.3)

func _generate_success_tone(frequency: float):
	# Simulácia úspešného tónu
	pass

func _generate_fail_tone(frequency: float):
	# Simulácia neúspešného tónu
	pass

func _normalizuj_text(text: String) -> String:
	# Odstráni diakritiku, medzery a prevedie na veľké písmená
	var normalizovany = text.to_upper()
	normalizovany = normalizovany.replace(" ", "")

	# Odstránenie diakritiky
	var diakritika = {
		"Á": "A", "Ä": "A", "Č": "C", "Ď": "D", "É": "E", "Ě": "E",
		"Í": "I", "Ľ": "L", "Ĺ": "L", "Ň": "N", "Ó": "O", "Ô": "O",
		"Ŕ": "R", "Š": "S", "Ť": "T", "Ú": "U", "Ů": "U", "Ý": "Y",
		"Ž": "Z"
	}

	for diakr in diakritika:
		normalizovany = normalizovany.replace(diakr, diakritika[diakr])

	return normalizovany

func _start_end_sequence():
	print("Spúšťa sa koncová sekvencia...")

	# Blesk efekt s animáciou
	var tween = create_tween()
	tween.tween_property(blesk, "color:a", 1.0, 0.05)
	tween.tween_property(blesk, "color:a", 0.0, 0.05)

	# Simulácia hromu pomocí screen shake
	_screen_shake()

	await get_tree().create_timer(0.2).timeout

	# Zastaviť ambientný zvuk
	zvuk_ambient.stop()

	# Simulácia paniky koní pomocí rýchlej animácie kočiara
	_animate_carriage_panic()

	# Krátka pauza
	await get_tree().create_timer(2.0).timeout

	print("Kočiš: 'Čo to bolo?! Kone sa vyplašili!'")
	await get_tree().create_timer(2.0).timeout

	# Prechod na ďalšiu scénu
	SceneManager.change_scene("res://scenes/scena_1_2.tscn")

func _screen_shake():
	var kociar_grafika = $Kociar_Grafika
	var original_pos = kociar_grafika.position
	var tween = create_tween()
	tween.set_loops(5)
	tween.tween_property(kociar_grafika, "position", original_pos + Vector2(10, 0), 0.05)
	tween.tween_property(kociar_grafika, "position", original_pos + Vector2(-10, 0), 0.05)
	tween.tween_callback(func(): kociar_grafika.position = original_pos)

func _animate_carriage_panic():
	var kociar_grafika = $Kociar_Grafika
	var tween = create_tween()
	tween.set_loops(3)
	tween.tween_property(kociar_grafika, "rotation", 0.1, 0.2)
	tween.tween_property(kociar_grafika, "rotation", -0.1, 0.2)
	tween.tween_callback(func(): kociar_grafika.rotation = 0)
