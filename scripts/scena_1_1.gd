extends Node2D

# Scéna 1.1 - Základná scéna pre Prekliateho Dedičstvo
# Autor: [Va<PERSON>e meno]
# Dátum: [Aktu<PERSON>lny dátum]

# Referencie na uzly
@onready var zvuk_ambient: AudioStreamPlayer = $Zvuk_Ambient
@onready var zvuk_efekty: AudioStreamPlayer = $Zvuk_Efekty
@onready var ui_hlavolam: CanvasLayer = $UI_Hlavolam
@onready var vstupne_pole: LineEdit = $UI_Hlavolam/VstupnePole
@onready var tlacitko_overit: Button = $UI_Hlavolam/TlacitkoOverit
@onready var blesk: ColorRect = $Blesk

# Správne riešenie hlavolamu
const SPRAVNE_RIESENIE = "GROFKAJEVKRYPTE"

# Called when the node enters the scene tree for the first time.
func _ready():
	print("Scéna 1.1 je načítaná")

	# Pripojenie sign<PERSON><PERSON>
	tlacitko_overit.pressed.connect(_on_tlacitko_overit_pressed)
	zvuk_efekty.finished.connect(_on_zvuk_efekty_finished)

	# Spustenie úvodnej sekvencie
	_start_intro_sequence()

func _start_intro_sequence():
	# Spustí ambientný zvuk (ak je nastavený)
	if zvuk_ambient.stream:
		zvuk_ambient.play()

	# Prehrá úvodný monológ (ak je nastavený)
	# TODO: Nastaviť narrator_intro.mp3 do zvuk_efekty.stream
	# zvuk_efekty.play()

func _on_zvuk_efekty_finished():
	# Po skončení monológu zviditeľní UI hlavolamu
	ui_hlavolam.visible = true

func _on_tlacitko_overit_pressed():
	var odpoved = vstupne_pole.text
	var normalizovana_odpoved = _normalizuj_text(odpoved)

	if normalizovana_odpoved == SPRAVNE_RIESENIE:
		print("Správna odpoveď!")
		# TODO: Prehrať puzzle_success.mp3
		ui_hlavolam.visible = false
		_start_end_sequence()
	else:
		print("Nesprávna odpoveď!")
		# TODO: Prehrať puzzle_fail.mp3
		vstupne_pole.text = ""

func _normalizuj_text(text: String) -> String:
	# Odstráni diakritiku, medzery a prevedie na veľké písmená
	var normalizovany = text.to_upper()
	normalizovany = normalizovany.replace(" ", "")

	# Odstránenie diakritiky
	var diakritika = {
		"Á": "A", "Ä": "A", "Č": "C", "Ď": "D", "É": "E", "Ě": "E",
		"Í": "I", "Ľ": "L", "Ĺ": "L", "Ň": "N", "Ó": "O", "Ô": "O",
		"Ŕ": "R", "Š": "S", "Ť": "T", "Ú": "U", "Ů": "U", "Ý": "Y",
		"Ž": "Z"
	}

	for diakr in diakritika:
		normalizovany = normalizovany.replace(diakr, diakritika[diakr])

	return normalizovany

func _start_end_sequence():
	print("Spúšťa sa koncová sekvencia...")

	# Zobrazí blesk na 0.1 sekundy
	blesk.color.a = 1.0
	# TODO: Prehrať thunder_close.mp3

	await get_tree().create_timer(0.1).timeout
	blesk.color.a = 0.0

	# TODO: Prehrať horse_panic_stop.mp3
	# Zastaviť ambientný zvuk
	zvuk_ambient.stop()

	# Krátka pauza
	await get_tree().create_timer(1.0).timeout

	# TODO: Prehrať coachman_fear.mp3
	# Po skončení dialógu prejsť na ďalšiu scénu
	await get_tree().create_timer(3.0).timeout  # Dočasne 3 sekundy

	# Prechod na ďalšiu scénu
	SceneManager.change_scene("res://scenes/scena_1_2.tscn")
