[gd_scene load_steps=3 format=3 uid="uid://bvxvqjqtqwqxr"]

[ext_resource type="Script" path="res://scripts/scena_1_1.gd" id="1_0hdqv"]
[ext_resource type="SpriteFrames" path="res://scenes/lampa_spriteframes.tres" id="2_3k8vw"]

[node name="Scena_1_1" type="Node2D"]
script = ExtResource("1_0hdqv")

[node name="Pozadie" type="TextureRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="<PERSON><PERSON>" type="AnimatedSprite2D" parent="."]
position = Vector2(540, 400)
sprite_frames = ExtResource("2_3k8vw")
animation = &"blikanie"
autoplay = "blikanie"

[node name="Blesk" type="ColorRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(1, 1, 1, 0)

[node name="Zvuk_Ambient" type="AudioStreamPlayer" parent="."]
autoplay = true
bus = &"Master"

[node name="Zvuk_Efekty" type="AudioStreamPlayer" parent="."]
bus = &"Master"

[node name="UI_Hlavolam" type="CanvasLayer" parent="."]
visible = false

[node name="List" type="TextureRect" parent="UI_Hlavolam"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -300.0
offset_right = 200.0
offset_bottom = 100.0
grow_horizontal = 2
grow_vertical = 2

[node name="VstupnePole" type="LineEdit" parent="UI_Hlavolam"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -150.0
offset_top = 120.0
offset_right = 150.0
offset_bottom = 160.0
grow_horizontal = 2
grow_vertical = 2
placeholder_text = "Zadajte odpoveď..."

[node name="TlacitkoOverit" type="Button" parent="UI_Hlavolam"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -60.0
offset_top = 180.0
offset_right = 60.0
offset_bottom = 220.0
grow_horizontal = 2
grow_vertical = 2
text = "Overiť"

[connection signal="pressed" from="UI_Hlavolam/TlacitkoOverit" to="." method="_on_tlacitko_overit_pressed"]
