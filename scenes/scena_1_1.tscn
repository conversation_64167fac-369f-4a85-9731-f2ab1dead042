[gd_scene load_steps=3 format=3 uid="uid://bvxvqjqtqwqxr"]

[ext_resource type="Script" path="res://scripts/scena_1_1.gd" id="1_0hdqv"]
[ext_resource type="SpriteFrames" path="res://scenes/lampa_spriteframes.tres" id="2_3k8vw"]

[node name="Scena_1_1" type="Node2D"]
script = ExtResource("1_0hdqv")

[node name="Pozadie" type="TextureRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="<PERSON><PERSON>" type="AnimatedSprite2D" parent="."]
position = Vector2(540, 400)
sprite_frames = ExtResource("2_3k8vw")
animation = &"blikanie"
autoplay = "blikanie"

[node name="Blesk" type="ColorRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(1, 1, 1, 0)
